import com.epoint.rule.action.StreamingIterator

/**
 * 简单的StreamingIterator测试脚本
 * 直接测试您提供的main函数功能
 */

def main(text, zishu) {
    def chgline = { line ->
        zishu += line.length()
        return line.replace("挖掘机", "XXX")
    } 

    def ret = new StreamingIterator(text, chgline)

    return [
        result: ret,
        zishu: zishu
    ]
}

// 测试执行
println "StreamingIterator 流式输出测试"
println "=" * 50

// 测试数据
def testCases = [
    [text: "这是一台挖掘机，挖掘机很强大。", zishu: 0],
    [text: "挖掘机在工地上工作，操作员驾驶挖掘机挖土。", zishu: 5],
    [text: "普通文本，没有特殊词汇。", zishu: 10],
    [text: "", zishu: 0],
    [text: "挖掘机挖掘机挖掘机", zishu: 2]
]

testCases.eachWithIndex { testCase, index ->
    println "\n测试用例 ${index + 1}:"
    println "输入文本: '${testCase.text}'"
    println "初始字数: ${testCase.zishu}"
    
    try {
        def result = main(testCase.text, testCase.zishu)
        println "处理后字数: ${result.zishu}"
        println "StreamingIterator对象: ${result.result}"
        println "对象类型: ${result.result.getClass().getName()}"
        
        // 尝试获取更多信息
        if (result.result.hasProperty('number')) {
            println "number属性: ${result.result.number}"
        }
        
    } catch (Exception e) {
        println "执行异常: ${e.message}"
        e.printStackTrace()
    }
    
    println "-" * 30
}

println "\n测试完成！"
