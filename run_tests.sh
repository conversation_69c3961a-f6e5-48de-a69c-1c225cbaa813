#!/bin/bash

echo "StreamingIterator 测试脚本运行器"
echo "=================================="

# 检查Groovy是否安装
if ! command -v groovy &> /dev/null; then
    echo "错误: 未找到Groovy命令。请确保Groovy已安装并在PATH中。"
    exit 1
fi

echo "Groovy版本信息:"
groovy --version
echo ""

# 运行简单测试
echo "1. 运行简单测试 (SimpleStreamTest.groovy):"
echo "----------------------------------------"
if [ -f "SimpleStreamTest.groovy" ]; then
    groovy SimpleStreamTest.groovy
else
    echo "错误: 找不到 SimpleStreamTest.groovy 文件"
fi

echo ""
echo ""

# 运行完整测试
echo "2. 运行完整测试 (StreamingIteratorTest.groovy):"
echo "--------------------------------------------"
if [ -f "StreamingIteratorTest.groovy" ]; then
    groovy StreamingIteratorTest.groovy test
else
    echo "错误: 找不到 StreamingIteratorTest.groovy 文件"
fi

echo ""
echo "测试运行完成！"
