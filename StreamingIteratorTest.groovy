import com.epoint.rule.action.StreamingIterator

/**
 * 流式输出测试脚本
 * 测试StreamingIterator的文本替换和字数统计功能
 */

def main(text, zishu) {
    def chgline = { line ->
        zishu += line.length()
        return line.replace("挖掘机", "XXX")
    } 

    def ret = new StreamingIterator(text, chgline)

    return [
        result: ret,
        zishu: zishu
    ]
}

/**
 * 测试函数
 */
def runTests() {
    println "=" * 60
    println "StreamingIterator 流式输出测试"
    println "=" * 60
    
    // 测试用例1：基本文本替换
    println "\n测试用例1：基本文本替换"
    println "-" * 30
    def testText1 = "这是一台挖掘机，挖掘机很强大。"
    def initialZishu1 = 0
    def result1 = main(testText1, initialZishu1)
    
    println "输入文本: ${testText1}"
    println "初始字数: ${initialZishu1}"
    println "处理后字数: ${result1.zishu}"
    println "StreamingIterator对象: ${result1.result}"
    println "StreamingIterator类型: ${result1.result.getClass().getName()}"
    
    // 测试用例2：多行文本
    println "\n测试用例2：多行文本处理"
    println "-" * 30
    def testText2 = """第一行：挖掘机在工地上工作
第二行：挖掘机操作员很专业
第三行：这台挖掘机性能优异"""
    def initialZishu2 = 10
    def result2 = main(testText2, initialZishu2)
    
    println "输入文本:"
    println testText2
    println "初始字数: ${initialZishu2}"
    println "处理后字数: ${result2.zishu}"
    println "StreamingIterator对象: ${result2.result}"
    
    // 测试用例3：无替换内容
    println "\n测试用例3：无需替换的文本"
    println "-" * 30
    def testText3 = "这是一段普通的文本，没有特殊词汇。"
    def initialZishu3 = 5
    def result3 = main(testText3, initialZishu3)
    
    println "输入文本: ${testText3}"
    println "初始字数: ${initialZishu3}"
    println "处理后字数: ${result3.zishu}"
    println "StreamingIterator对象: ${result3.result}"
    
    // 测试用例4：空文本
    println "\n测试用例4：空文本处理"
    println "-" * 30
    def testText4 = ""
    def initialZishu4 = 0
    def result4 = main(testText4, initialZishu4)
    
    println "输入文本: '${testText4}'"
    println "初始字数: ${initialZishu4}"
    println "处理后字数: ${result4.zishu}"
    println "StreamingIterator对象: ${result4.result}"
    
    // 测试用例5：大量重复词汇
    println "\n测试用例5：大量重复词汇"
    println "-" * 30
    def testText5 = "挖掘机挖掘机挖掘机，三台挖掘机一起工作，挖掘机效率很高。"
    def initialZishu5 = 20
    def result5 = main(testText5, initialZishu5)
    
    println "输入文本: ${testText5}"
    println "初始字数: ${initialZishu5}"
    println "处理后字数: ${result5.zishu}"
    println "StreamingIterator对象: ${result5.result}"
    
    println "\n" + "=" * 60
    println "测试完成"
    println "=" * 60
}

/**
 * 测试StreamingIterator的迭代功能
 */
def testIteratorFunctionality() {
    println "\n" + "=" * 60
    println "StreamingIterator 迭代功能测试"
    println "=" * 60
    
    def testText = "挖掘机在工地上挖土，挖掘机很有用。"
    def zishu = 0
    
    def chgline = { line ->
        zishu += line.length()
        println "处理行: ${line}"
        def processed = line.replace("挖掘机", "XXX")
        println "处理后: ${processed}"
        return processed
    }
    
    def iterator = new StreamingIterator(testText, chgline)
    
    println "\n开始迭代处理..."
    println "原始文本: ${testText}"
    println "字数统计: ${zishu}"
    
    // 如果StreamingIterator支持迭代，尝试迭代
    try {
        if (iterator.hasNext()) {
            println "\nStreamingIterator支持迭代，开始迭代:"
            while (iterator.hasNext()) {
                def item = iterator.next()
                println "迭代项: ${item}"
            }
        } else {
            println "\nStreamingIterator不支持hasNext()方法"
        }
    } catch (Exception e) {
        println "\n迭代测试异常: ${e.message}"
        println "可能StreamingIterator不支持标准迭代接口"
    }
    
    // 尝试其他可能的方法
    try {
        println "\n尝试调用toString()方法:"
        println "toString(): ${iterator.toString()}"
    } catch (Exception e) {
        println "toString()调用异常: ${e.message}"
    }
    
    try {
        println "\n尝试获取可用方法:"
        def methods = iterator.getClass().getMethods()
        println "可用方法数量: ${methods.length}"
        methods.each { method ->
            if (!method.name.startsWith("get") && !method.name.startsWith("set") && 
                !method.name.equals("wait") && !method.name.equals("notify") && 
                !method.name.equals("notifyAll") && !method.name.equals("hashCode") &&
                !method.name.equals("equals") && !method.name.equals("toString") &&
                !method.name.equals("getClass")) {
                println "  - ${method.name}(${method.parameterTypes.collect{it.simpleName}.join(', ')})"
            }
        }
    } catch (Exception e) {
        println "获取方法列表异常: ${e.message}"
    }
}

// 执行测试
if (args.length > 0 && args[0] == "test") {
    runTests()
    testIteratorFunctionality()
} else {
    // 如果没有参数，执行默认测试
    println "使用方法: groovy StreamingIteratorTest.groovy test"
    println "或者直接调用main函数进行单独测试"
    
    // 执行一个简单的示例
    def sampleText = "这台挖掘机很厉害，挖掘机操作简单。"
    def sampleZishu = 0
    def sampleResult = main(sampleText, sampleZishu)
    
    println "\n示例测试:"
    println "输入: ${sampleText}"
    println "字数: ${sampleResult.zishu}"
    println "结果: ${sampleResult.result}"
}
