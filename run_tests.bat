@echo off
chcp 65001 >nul

echo StreamingIterator 测试脚本运行器
echo ==================================

REM 检查Groovy是否安装
groovy --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Groovy命令。请确保Groovy已安装并在PATH中。
    pause
    exit /b 1
)

echo Groovy版本信息:
groovy --version
echo.

REM 运行简单测试
echo 1. 运行简单测试 (SimpleStreamTest.groovy):
echo ----------------------------------------
if exist "SimpleStreamTest.groovy" (
    groovy SimpleStreamTest.groovy
) else (
    echo 错误: 找不到 SimpleStreamTest.groovy 文件
)

echo.
echo.

REM 运行完整测试
echo 2. 运行完整测试 (StreamingIteratorTest.groovy):
echo --------------------------------------------
if exist "StreamingIteratorTest.groovy" (
    groovy StreamingIteratorTest.groovy test
) else (
    echo 错误: 找不到 StreamingIteratorTest.groovy 文件
)

echo.
echo 测试运行完成！
pause
